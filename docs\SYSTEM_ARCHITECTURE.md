# 多進程伺服器系統 - 技術架構

## 目錄
1. [系統概述](#系統概述)
2. [架構組件](#架構組件)
3. [數據流](#數據流)
4. [Docker 編排](#docker-編排)
5. [多進程伺服器實作](#多進程伺服器實作)
6. [客戶端實作](#客戶端實作)
7. [性能優化](#性能優化)
8. [整合指南](#整合指南)
9. [生產環境考量](#生產環境考量)
10. [故障排除](#故障排除)

## 系統概述

此系統展示了一個高性能多進程伺服器架構，設計用於處理多個並發客戶端的高頻數據傳輸（每秒 10,050 個封包，16 位元組封包）。系統使用 Python 多進程、Docker 容器化和優化網路來實現可擴展的性能。

### 關鍵特性
- **目標性能**：每個客戶端每秒 10,050 個封包
- **封包大小**：16 位元組
- **批次大小**：每批 804 個封包
- **並發客戶端**：最多 10 個客戶端
- **架構**：主從多進程模式
- **容器化**：具備優化資源分配的 Docker
- **監控**：即時性能指標和分析

## 架構組件

### 1. 核心組件

```
┌─────────────────────────────────────────────────────────────┐
│                    SYSTEM ARCHITECTURE                      │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  ┌─────────────────┐    ┌─────────────────────────────────┐  │
│  │   Docker Host   │    │        Application Layer       │  │
│  │                 │    │                                 │  │
│  │ ┌─────────────┐ │    │ ┌─────────────────────────────┐ │  │
│  │ │   Server    │ │    │ │     Multiprocessing        │ │  │
│  │ │ Container   │ │◄───┤ │        Server              │ │  │
│  │ │             │ │    │ │   (Master Process)         │ │  │
│  │ └─────────────┘ │    │ └─────────────────────────────┘ │  │
│  │                 │    │                                 │  │
│  │ ┌─────────────┐ │    │ ┌─────────────────────────────┐ │  │
│  │ │  Client-1   │ │    │ │      Worker Process 1        │ │  │
│  │ │ Container   │ │◄───┤ │                             │ │  │
│  │ └─────────────┘ │    │ └─────────────────────────────┘ │  │
│  │                 │    │                                 │  │
│  │ ┌─────────────┐ │    │ ┌─────────────────────────────┐ │  │
│  │ │  Client-2   │ │    │ │      Worker Process 2        │ │  │
│  │ │ Container   │ │◄───┤ │                             │ │  │
│  │ └─────────────┘ │    │ └─────────────────────────────┘ │  │
│  │                 │    │                                 │  │
│  │ ┌─────────────┐ │    │ ┌─────────────────────────────┐ │  │
│  │ │  Client-N   │ │    │ │      Worker Process N        │ │  │
│  │ │ Container   │ │◄───┤ │                             │ │  │
│  │ └─────────────┘ │    │ └─────────────────────────────┘ │  │
│  └─────────────────┘    └─────────────────────────────────┘  │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

### 2. 組件職責

| 組件 | 職責 |
|-----------|----------------|
| **Docker Compose** | 編排、資源管理、網路 |
| **伺服器容器** | 主進程、連接處理、工作進程管理 |
| **客戶端容器** | 數據生成、傳輸、性能監控 |
| **工作進程** | 數據處理、客戶端通信 |
| **性能監控器** | 系統指標、資源追蹤 |

## 數據流

### 1. 系統初始化流程

```mermaid
sequenceDiagram
    participant DC as Docker Compose
    participant SC as Server Container
    participant CC as Client Containers
    participant WP as Worker Processes
    participant PM as Performance Monitor

    DC->>SC: Start server container
    SC->>SC: Initialize server socket
    SC->>WP: Spawn worker processes
    WP->>WP: Initialize worker queues
    SC->>PM: Start performance monitoring
    
    Note over DC,PM: Wait for server health check
    
    DC->>CC: Start client containers (staggered)
    CC->>SC: Connect to server
    SC->>WP: Dispatch connections to workers
    WP->>CC: Accept client connections
```

### 2. 運行時數據流

```mermaid
sequenceDiagram
    participant C as Client
    participant S as Server
    participant W as Worker
    participant Q as Queue
    participant M as Monitor

    C->>S: Connect to server
    S->>Q: Add connection to queue
    W->>Q: Get connection from queue
    W->>C: Accept client connection
    
    loop Data Transmission
        C->>W: Send packet batch
        W->>W: Process packet data
        W->>M: Update statistics
        M->>M: Log performance metrics
    end
    
    C->>W: Close connection
    W->>M: Final statistics
```

### 3. 封包處理流程

```
客戶端封包生成：
┌─────────────┐    ┌──────────────┐    ┌─────────────┐
│   計時器    │───▶│   批次      │───▶│   Socket    │
│  (10kHz)   │    │  組裝       │    │   發送      │
└─────────────┘    └──────────────┘    └─────────────┘
       │                   │                   │
       ▼                   ▼                   ▼
┌─────────────┐    ┌──────────────┐    ┌─────────────┐
│  速率      │    │  32位元組   │    │  網路      │
│ 控制       │    │  封包       │    │  緩衝區     │
└─────────────┘    └──────────────┘    └─────────────┘

伺服器封包處理：
┌─────────────┐    ┌──────────────┐    ┌─────────────┐
│   Socket   │───▶│   工作      │───▶│ 統計資料    │
│   接收     │    │  進程       │    │  更新       │
└─────────────┘    └──────────────┘    └─────────────┘
       │                   │                   │
       ▼                   ▼                   ▼
┌─────────────┐    ┌──────────────┐    ┌─────────────┐
│  網路      │    │   數據      │    │  監控      │
│  緩衝區     │    │  處理       │    │  記錄       │
└─────────────┘    └──────────────┘    └─────────────┘
```

## Docker 編排

### 1. Docker Compose 配置

系統使用 Docker Compose 進行編排，具有以下關鍵特性：

#### 伺服器配置
```yaml
server:
  build: .
  command: python server.py --host 0.0.0.0 --port 8888 --workers 4 --max-clients 10
  ports:
    - "8888:8888"
  deploy:
    resources:
      limits:
        cpus: '6.0'
        memory: 4G
      reservations:
        cpus: '3.0'
        memory: 2G
  healthcheck:
    test: ["CMD", "python", "-c", "import socket; s=socket.socket(); s.connect(('localhost', 8888)); s.close()"]
    interval: 5s
    timeout: 3s
    retries: 3
    start_period: 10s
```

#### 客戶端配置
```yaml
client-1:
  build: .
  command: python client_simulator.py --host server --port 8888 --clients 1 --rate 6000 --duration 30
  depends_on:
    server:
      condition: service_healthy
  deploy:
    resources:
      limits:
        cpus: '2.0'
        memory: 1.5G
      reservations:
        cpus: '1.5'
        memory: 1G
  environment:
    - CLIENT_STARTUP_DELAY=0
```

### 2. 建置流程

```bash
# 1. Docker 建置映像檔
docker build -t multiprocess_test .

# 2. Docker Compose 啟動服務
docker-compose up -d

# 3. 健康檢查確保伺服器就緒
# 4. 客戶端以錯開延遲啟動
# 5. 建立連接
# 6. 開始數據傳輸
```

### 3. 網路架構

```
┌─────────────────────────────────────────────────────────────┐
│                    Docker Network                          │
│                   (optimized_network)                       │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐     │
│  │   Server    │    │  Client-1   │    │  Client-2   │     │
│  │ 172.20.0.2  │◄───┤ 172.20.0.3  │    │ 172.20.0.4  │     │
│  │   :8888     │    │             │    │             │     │
│  └─────────────┘    └─────────────┘    └─────────────┘     │
│                                                             │
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐     │
│  │  Client-3   │    │  Client-4   │    │  Client-5   │     │
│  │ 172.20.0.5  │    │ 172.20.0.6  │    │ 172.20.0.7  │     │
│  └─────────────┘    └─────────────┘    └─────────────┘     │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

## 多進程伺服器實作

### 1. 架構模式：主從模式

伺服器實作了**主從**多進程模式：

```python
class MultiprocessingServer:
    def __init__(self, host, port, num_workers=4, max_clients=10):
        self.host = host
        self.port = port
        self.num_workers = num_workers
        self.max_clients = max_clients
        
        # Inter-process communication
        self.connection_queue = multiprocessing.Queue()
        self.stats_queue = multiprocessing.Queue()
        
        # Worker processes
        self.workers = []
        self.running = False
```

### 2. 進程管理

#### 主進程職責
1. **Socket 管理**：接受傳入連接
2. **負載平衡**：將連接分配給工作進程
3. **進程監控**：監控工作進程健康狀態
4. **統計聚合**：收集和報告指標
5. **資源管理**：處理進程生命週期

#### 工作進程職責
1. **連接處理**：管理客戶端連接
2. **數據處理**：處理傳入封包
3. **統計收集**：追蹤性能指標
4. **錯誤處理**：管理連接錯誤

### 3. 進程間通信

```python
# Connection Distribution
def start(self):
    # Start worker processes
    for i in range(self.num_workers):
        worker = multiprocessing.Process(
            target=worker_process,
            args=(i, self.connection_queue, self.stats_queue)
        )
        worker.start()
        self.workers.append(worker)
    
    # Main accept loop
    while self.running:
        try:
            client_socket, client_address = self.server_socket.accept()
            # Distribute to worker via queue
            self.connection_queue.put((client_socket, client_address))
        except BlockingIOError:
            time.sleep(0.001)  # Non-blocking accept
```

### 4. 工作進程實作

```python
def worker_process(worker_id, connection_queue, stats_queue):
    """Worker process for handling client connections"""
    
    # Set process priority for better performance
    try:
        current_process = psutil.Process()
        current_process.nice(psutil.HIGH_PRIORITY_CLASS)
    except Exception as e:
        print(f"Worker {worker_id}: Could not set process priority: {e}")
    
    # Process connections from queue
    while True:
        try:
            # Get connection from queue
            client_socket, client_address = connection_queue.get(timeout=1)
            
            # Handle client connection
            handle_client(client_socket, client_address, worker_id, stats_queue)
            
        except queue.Empty:
            continue
        except Exception as e:
            logger.error(f"Worker {worker_id} error: {e}")
```

### 5. 連接處理

```python
def handle_client(client_socket, client_address, worker_id, stats_queue):
    """Handle individual client connection"""
    
    # Optimize client socket
    client_socket.setsockopt(socket.IPPROTO_TCP, socket.TCP_NODELAY, 1)
    client_socket.setsockopt(socket.SOL_SOCKET, socket.SO_KEEPALIVE, 1)
    client_socket.setsockopt(socket.SOL_SOCKET, socket.SO_RCVBUF, 131072)
    client_socket.setsockopt(socket.SOL_SOCKET, socket.SO_SNDBUF, 131072)
    
    # Process packets
    while True:
        try:
            data = client_socket.recv(4096)
            if not data:
                break
            
            # Process received data
            process_packets(data, worker_id, stats_queue)
            
        except ConnectionError:
            break
        except Exception as e:
            logger.error(f"Client handling error: {e}")
            break
    
    client_socket.close()
```

## 客戶端實作

### 1. 高性能客戶端架構

```python
class OptimizedClient:
    def __init__(self, client_id, target_rate=10000.0):
        self.client_id = client_id
        self.target_rate = target_rate
        
        # Performance optimizations
        self.packet_data = b'X' * 32  # Pre-allocated packet
        self.batch_size = max(1, int(target_rate / 200))  # Batch packets
        self.send_timeout = 0.001  # 1ms timeout
```

### 2. 數據傳輸策略

#### 批次處理
- **批次大小**：`target_rate / 200`（例如，6000 Hz 對應 30 個封包）
- **傳輸速率**：由精確計時控制
- **錯誤處理**：具有指數退避的重試邏輯

#### 連接管理
```python
def connect(self, host, port):
    """Enhanced connection with retry logic"""
    
    max_retries = 10
    retry_delay = 0.5
    
    for attempt in range(max_retries):
        try:
            self.socket.connect((host, port))
            return True
        except (BlockingIOError, ConnectionRefusedError, OSError) as e:
            if attempt < max_retries - 1:
                time.sleep(retry_delay)
                retry_delay = min(retry_delay * 1.5, 2.0)
            else:
                return False
```

### 3. 速率控制實作

```python
def _transmission_loop(self, duration):
    """Precise rate-controlled transmission"""
    
    packet_interval = 1.0 / self.target_rate
    start_time = time.time()
    next_send_time = start_time
    
    while time.time() - start_time < duration:
        current_time = time.time()
        
        if current_time >= next_send_time:
            self._send_batch()
            next_send_time += packet_interval * self.batch_size
        else:
            # Precise timing control
            sleep_time = next_send_time - current_time
            time.sleep(sleep_time)
```

## 性能優化

### 1. Socket 優化

```python
# Server socket optimizations
self.server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
self.server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_KEEPALIVE, 1)
self.server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_RCVBUF, 131072)
self.server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_SNDBUF, 131072)
self.server_socket.setblocking(False)

# Client socket optimizations
client_socket.setsockopt(socket.IPPROTO_TCP, socket.TCP_NODELAY, 1)
client_socket.setsockopt(socket.SOL_SOCKET, socket.SO_KEEPALIVE, 1)
client_socket.setsockopt(socket.SOL_SOCKET, socket.SO_RCVBUF, 131072)
client_socket.setsockopt(socket.SOL_SOCKET, socket.SO_SNDBUF, 131072)
```

### 2. 進程優化

```python
# Process priority
current_process = psutil.Process()
current_process.nice(psutil.HIGH_PRIORITY_CLASS)

# CPU affinity
os.sched_setaffinity(0, range(multiprocessing.cpu_count()))
```

### 3. Docker 資源管理

```yaml
# Server resources
deploy:
  resources:
    limits:
      cpus: '6.0'
      memory: 4G
    reservations:
      cpus: '3.0'
      memory: 2G

# Client resources
deploy:
  resources:
    limits:
      cpus: '2.0'
      memory: 1.5G
    reservations:
      cpus: '1.5'
      memory: 1G
```

## Integration Guidelines

### 1. Production Deployment

#### Server Integration
```python
# Example integration in production application
class ProductionServer:
    def __init__(self, config):
        self.server = MultiprocessingServer(
            host=config.host,
            port=config.port,
            num_workers=config.workers,
            max_clients=config.max_clients
        )
        
    def start(self):
        # Add production-specific initialization
        self.setup_logging()
        self.setup_monitoring()
        self.setup_health_checks()
        
        # Start the multiprocessing server
        self.server.start()
    
    def setup_logging(self):
        # Configure production logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('server.log'),
                logging.StreamHandler()
            ]
        )
```

#### Client Integration
```python
# Example client integration
class ProductionClient:
    def __init__(self, server_config):
        self.client = OptimizedClient(
            client_id=server_config.client_id,
            target_rate=server_config.target_rate
        )
        
    def connect_and_transmit(self, data_source):
        if self.client.connect(server_config.host, server_config.port):
            self.client.start_transmission(
                duration=server_config.duration,
                data_source=data_source
            )
```

### 2. Scalability Considerations

#### Horizontal Scaling
- **Load Balancer**: Distribute clients across multiple server instances
- **Database Integration**: Store metrics in time-series database
- **Message Queues**: Use Redis/RabbitMQ for inter-process communication

#### Vertical Scaling
- **CPU Cores**: Increase worker processes based on available cores
- **Memory**: Adjust batch sizes based on available RAM
- **Network**: Optimize buffer sizes for network capacity

### 3. Monitoring Integration

```python
# Production monitoring integration
class ProductionMonitor:
    def __init__(self):
        self.metrics_collector = MetricsCollector()
        self.alert_manager = AlertManager()
        
    def collect_metrics(self, server_stats):
        # Collect and forward metrics
        self.metrics_collector.record(
            'packets_processed', server_stats.total_packets
        )
        self.metrics_collector.record(
            'active_connections', server_stats.active_connections
        )
        
        # Check for alerts
        if server_stats.error_rate > 0.1:
            self.alert_manager.send_alert("High error rate detected")
```

## Production Considerations

### 1. Security

#### Network Security
```python
# TLS/SSL integration
import ssl

def create_secure_server():
    context = ssl.create_default_context(ssl.Purpose.CLIENT_AUTH)
    context.load_cert_chain('server.crt', 'server.key')
    
    server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    secure_socket = context.wrap_socket(server_socket, server_side=True)
    return secure_socket
```

#### Authentication
```python
# Client authentication
def authenticate_client(client_socket):
    # Implement authentication logic
    auth_token = client_socket.recv(1024)
    if validate_token(auth_token):
        return True
    return False
```

### 2. Error Handling

#### Graceful Shutdown
```python
def graceful_shutdown(signum, frame):
    """Handle graceful shutdown"""
    logger.info("Received shutdown signal")
    
    # Stop accepting new connections
    server.running = False
    
    # Wait for workers to finish
    for worker in workers:
        worker.join(timeout=5)
    
    # Close server socket
    server_socket.close()
```

#### Connection Recovery
```python
def handle_connection_error(client_socket, error):
    """Handle connection errors gracefully"""
    logger.error(f"Connection error: {error}")
    
    try:
        client_socket.close()
    except:
        pass
    
    # Update statistics
    stats_queue.put({
        'type': 'connection_error',
        'timestamp': time.time(),
        'error': str(error)
    })
```

### 3. Performance Tuning

#### System-Level Optimizations
```bash
# Increase file descriptor limits
ulimit -n 65536

# Optimize network parameters
echo 'net.core.rmem_max = 134217728' >> /etc/sysctl.conf
echo 'net.core.wmem_max = 134217728' >> /etc/sysctl.conf
sysctl -p
```

#### Application-Level Tuning
```python
# Tune batch sizes based on system capacity
def calculate_optimal_batch_size(target_rate, system_capacity):
    base_batch_size = target_rate / 100
    
    if system_capacity > 0.8:  # High load
        return base_batch_size * 0.5
    elif system_capacity > 0.5:  # Medium load
        return base_batch_size * 0.8
    else:  # Low load
        return base_batch_size
```

## Troubleshooting

### 1. Common Issues

#### Connection Refused
```bash
# Check if server is running
docker ps | grep server

# Check server logs
docker logs multiprocess_test-server-1

# Check network connectivity
docker exec -it multiprocess_test-client-1-1 ping server
```

#### Performance Issues
```bash
# Monitor resource usage
docker stats

# Check system resources
htop
iostat -x 1

# Analyze network performance
netstat -i
```

#### Memory Issues
```bash
# Check memory usage
free -h
docker stats --no-stream

# Monitor memory leaks
valgrind --tool=memcheck python server.py
```

### 2. Debugging Tools

#### Server Debugging
```python
# Enable debug logging
logging.basicConfig(level=logging.DEBUG)

# Add debug prints
def debug_worker_process(worker_id, connection_queue, stats_queue):
    while True:
        try:
            client_socket, client_address = connection_queue.get(timeout=1)
            print(f"Worker {worker_id} handling {client_address}")
            # ... rest of the code
```

#### Client Debugging
```python
# Add connection debugging
def debug_connect(self, host, port):
    print(f"Attempting to connect to {host}:{port}")
    # ... connection logic
    print(f"Connection successful: {self.socket}")
```

### 3. Performance Analysis

#### Metrics Collection
```python
# Collect detailed metrics
class DetailedMonitor:
    def __init__(self):
        self.metrics = {
            'packet_rates': [],
            'error_rates': [],
            'connection_times': [],
            'memory_usage': []
        }
    
    def record_metric(self, metric_type, value):
        self.metrics[metric_type].append({
            'timestamp': time.time(),
            'value': value
        })
```

#### Profiling
```python
# Profile server performance
import cProfile

def profile_server():
    profiler = cProfile.Profile()
    profiler.enable()
    
    # Run server
    server.start()
    
    profiler.disable()
    profiler.dump_stats('server_profile.prof')
```

## Conclusion

This multiprocessing server system provides a robust foundation for high-performance, concurrent data processing applications. The architecture scales well with proper resource allocation and can be adapted for various production scenarios.

Key takeaways:
- **Scalability**: Master-worker pattern allows horizontal scaling
- **Performance**: Optimized networking and process management
- **Reliability**: Error handling and connection recovery
- **Monitoring**: Comprehensive metrics and debugging tools
- **Production Ready**: Security, authentication, and deployment considerations

For production deployment, consider:
- Load balancing across multiple server instances
- Database integration for persistent metrics
- Security hardening and authentication
- Automated monitoring and alerting
- Container orchestration with Kubernetes or Docker Swarm
